﻿/* Original styles */
.medical-sections .e-list-item {
    padding: 12px 0;
    color: #5f6368;
    font-size: 14px;
    border-bottom: 1px solid #ebedf0;
}

.custom-disabled-list-item.mud-list-item-disabled {
    color: silver !important;
    opacity: 1 !important;
    cursor: default;
    pointer-events: none;
}

.custom-disabled-listheader-item.mud-list-item-disabled {
    color: grey !important;
    opacity: 1 !important;
    cursor: default;
    pointer-events: none;
}

.headers-container {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    background-color: #fafafa;
    margin-bottom: 16px;
}

.compact-textfield .mud-input-control {
    height: 36px !important;
    min-height: 36px !important;
}

.compact-textfield .mud-input-control-input-container {
    height: 36px !important;
    min-height: 36px !important;
}

.compact-textfield .mud-input {
    height: 100% !important;
    padding: 0 6px !important;
    font-size: 13px !important;
    display: flex !important;
    align-items: center !important;
    line-height: normal !important;
}

.compact-textfield .mud-input-label {
    font-size: 11px !important;
    top: -8px !important;
}


/* 2. Visit Type dropdown - improved visibility */
.visit-type-dropdown {
    border: 2px solid #1976d2 !important;
    border-radius: 6px !important;
    background-color: white !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

    .visit-type-dropdown .e-input-group {
        border: none !important;
        background: transparent !important;
        height: 36px !important;
    }

        .visit-type-dropdown .e-input-group.e-control-wrapper {
            height: 36px !important;
        }

    .visit-type-dropdown .e-input {
        height: 36px !important;
        padding: 0 12px !important;
        border: none !important;
        background: transparent !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #333 !important;
    }

    .visit-type-dropdown .e-ddl-icon {
        color: #1976d2 !important;
        font-size: 16px !important;
    }

/* 3. Template Headers title - bigger size and moved left */
.template-headers-title {
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    margin-left: 4px !important;
    color: #1976d2 !important;
}

/* 4. Header Title label - better alignment */
.header-title-label {
    margin-left: 32px !important;
    margin-right: auto !important;
    font-weight: 500 !important;
    text-align: left !important;
}

/* Better alignment for Header Title row */
.mud-card-content .d-flex.justify-content-between .header-title-label {
    flex: 1 !important;
    margin-left: 0 !important;
    margin-right: 16px !important;
}

/* Ensure proper spacing in header title section */
.mud-card-content .d-flex.justify-content-between .mud-item.ml-auto {
    margin-left: 0 !important;
    flex: 2 !important;
}

/* 5. Header section cards - better segregation between headers */
.header-section-card {
    background-color: white;
    border: 2px solid #e3f2fd;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

    .header-section-card:nth-child(odd) {
        background-color: #fafafa;
        border-color: #e8f5e8;
    }

    .header-section-card:nth-child(even) {
        background-color: #fff;
        border-color: #e3f2fd;
    }

    .header-section-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        border-radius: 12px 12px 0 0;
    }

    .header-section-card:last-child {
        margin-bottom: 0;
    }

/* 6. Reduced spacing between Sections and Section Settings */
.mud-grid .mud-item[class*="sm-2"] {
    padding-right: 8px !important;
}

.mud-grid .mud-item[class*="sm-10"] {
    padding-left: 8px !important;
}

/* Reduce overall grid spacing */
.mud-grid[class*="GutterSize"] {
    margin: -4px !important;
}

    .mud-grid[class*="GutterSize"] > .mud-item {
        padding: 4px !important;
    }

/* Header alignment improvements */
.header-section-card .mud-grid .mud-item:last-child {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

/* Section title styling */
.header-section-card .mud-text[style*="font-weight:bold"] {
    font-size: 1.1rem !important;
    color: #1976d2 !important;
}

/* Improved form layout */
.mud-card-content .mud-grid .mud-item {
    margin-bottom: 12px;
}

/* Button styling improvements */
.mud-button-group .mud-button {
    font-size: 0.875rem !important;
    padding: 6px 12px !important;
}

/* Enhanced visual hierarchy */
.mud-text.mud-typography-h5 {
    color: #1976d2;
    font-weight: 600;
}

.mud-text.mud-typography-h6 {
    color: #424242;
    font-weight: 500;
}

/* Improved card styling */
.mud-card {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
}

.mud-card-header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

/* Better focus states */
.mud-input:focus,
.e-input:focus {
    border-color: #1976d2 !important;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
}

/* Enhanced accessibility */
.mud-button:focus-visible,
.mud-icon-button:focus-visible {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
}

/* Responsive design improvements */
@media (max-width: 600px) {
    .ml-6 {
        margin-left: 0 !important;
    }

    .pa-4 {
        padding: 8px !important;
    }

    .template-headers-title {
        font-size: 1.2rem !important;
        margin-left: 2px !important;
    }

    .header-title-label {
        margin-left: auto !important;
        margin-right: 4px !important;
    }

    .header-section-card {
        padding: 16px;
        margin-bottom: 16px;
    }

    .compact-textfield .mud-input {
        height: 26px !important;
        padding: 2px 4px !important;
        font-size: 12px !important;
    }

    .compact-textfield .mud-input-control {
        height: 26px !important;
        min-height: 26px !important;
    }

    .visit-type-dropdown {
        height: 32px !important;
    }

        .visit-type-dropdown .e-input {
            height: 32px !important;
            padding: 0 8px !important;
        }

    /* Reduce spacing on mobile */
    .mud-grid .mud-item[class*="sm-2"] {
        padding-right: 4px !important;
    }

    .mud-grid .mud-item[class*="sm-10"] {
        padding-left: 4px !important;
    }
}

@media (max-width: 960px) {
    .mud-grid .mud-item {
        margin-bottom: 12px;
    }

        /* Stack sections vertically on medium screens */
        .mud-grid .mud-item[class*="sm-2"] {
            flex: 0 0 100% !important;
            max-width: 100% !important;
            margin-bottom: 8px !important;
        }

        .mud-grid .mud-item[class*="sm-10"] {
            flex: 0 0 100% !important;
            max-width: 100% !important;
        }
}

@media (min-width: 961px) and (max-width: 1200px) {
    .header-section-card {
        padding: 18px;
    }

    .template-headers-title {
        font-size: 1.3rem !important;
    }
}

/* Better spacing for mobile */
@media (max-width: 480px) {
    .mud-container {
        padding: 8px !important;
    }

    .headers-container {
        padding: 12px;
    }

    .header-section-card {
        padding: 12px;
    }

    .mud-grid {
        margin: 0 !important;
    }

    .template-headers-title {
        font-size: 1.1rem !important;
    }
}

/* Additional improvements for better UX */
.mud-input-control {
    margin-bottom: 6px;
}

.mud-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Smooth transitions */
.header-section-card,
.mud-card,
.mud-button {
    transition: all 0.2s ease-in-out;
}

    .header-section-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

/* Improved section spacing */
.medical-sections {
    padding-right: 12px;
    border-right: 1px solid #e0e0e0;
}

/* Better alignment for form elements */
.mud-card-content .d-flex.justify-content-between {
    align-items: center;
    margin-bottom: 8px;
}

/* Enhanced button styling */
.mud-button.mud-button-filled.mud-button-color-primary {
    background: linear-gradient(45deg, #1976d2, #42a5f5);
    box-shadow: 0 3px 6px rgba(25, 118, 210, 0.3);
}

    .mud-button.mud-button-filled.mud-button-color-primary:hover {
        background: linear-gradient(45deg, #1565c0, #1976d2);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
    }

.header-section-card .mud-grid:first-child {
    margin-top: 8px !important;
    align-items: center;
    min-height: 48px; 
}

.header-section-card .mud-button {
    display: flex;
    align-items: center;
    padding: 8px 12px;
}

.header-section-card .mud-icon-button {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    width: 40px;
}

.header-section-card .mud-grid.mt-none {
    margin-top: 0 !important;
}