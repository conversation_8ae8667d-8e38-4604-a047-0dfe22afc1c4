﻿@page "/templates"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaUIViewModels.ViewModels
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "templatesAccessPolicy")]
@using MudBlazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns
@using TeyaWebApp.Components.Layout
@using Syncfusion.Blazor.Grids
@using System.Text.Json
@using TeyaWebApp.TeyaAIScribeResources
@inject ITemplateService TemplateService
@inject IVisitTypeService VisitTypeService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ILogger<Templates> Logger
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject IMemberService MemberService
@using Syncfusion.Blazor.Navigations
@layout Admin
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Style="padding: 24px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
    <MudGrid>
        <MudItem xs="12">
            <MudPaper Class="pa-4" Elevation="0">
                <MudText Typo="Typo.h6" Class="mb-4 ml-6">Available Templates</MudText>
                <MudItem xs="12">
                    <div>
                        <SfDropDownList TValue="string" TItem="string"
                                        Placeholder="Select Visit Type"
                                        DataSource="@data"
                                        @bind-Value="@DropDownValue"
                                        Width="300px"
                                        CssClass="visit-type-dropdown">
                            <DropDownListEvents TValue="string" TItem="string" OnValueSelect="@OnValueSelecthandler" />
                        </SfDropDownList>

                        <SfButton OnClick="ResetSelection"
                                  CssClass="e-primary"
                                  style="height: 30px; padding: 0 16px;">
                            Reset
                        </SfButton>
                    </div>
                </MudItem>
                <div style="max-width: 1200px;">
                    <GenericGrid TValue="TemplateData" DataSource="@ProviderData" GridLines="GridLine.Both">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel" />
                        <GridPageSettings PageSize="10" />
                        <GridColumns>
                            <GridColumn Field="@nameof(TemplateData.IsDefault)" HeaderText="@Localizer["Default"]" Width="150" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                    }
                                    <SfCheckBox Checked="@templateData?.IsDefault" @onchange="@(args => OnIsDefaultChange(args, templateData))" />
                                </Template>
                            </GridColumn>
                            <GridColumn Field="@nameof(TemplateData.VisitType)" HeaderText="@Localizer["Visit Type"]" Width="200" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                    }
                                    <span>@(string.IsNullOrEmpty(templateData?.VisitType) ? @Localizer["Not Assigned"] : templateData.VisitType)</span>
                                </Template>
                            </GridColumn>
                            <GridColumn Field="@nameof(TemplateData.TemplateName)" HeaderText="@Localizer["Template Name"]" Width="200" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                    }
                                    <span style="cursor: pointer;" @onclick="() => EditTemplate(templateData)">
                                        @templateData.TemplateName
                                    </span>
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Sections" Width="500" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                        var templateDictionary = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, TemplateField>>>(templateData.Template);
                                        List<string> Keys = new List<string>();
                                        if (templateDictionary != null)
                                        {
                                            Keys = templateDictionary.Values
                                            .Where(innerDict => innerDict != null)
                                            .SelectMany(innerDict => innerDict.Keys)
                                            .ToList();
                                        }
                                        string allKeys = string.Join(", ", Keys);
                                        int maxLength = 50;
                                        string displayText = allKeys.Length > maxLength ? allKeys.Substring(0, maxLength) + "..." : allKeys;
                                    }

                                    <span style="cursor: pointer;" @onclick="() => EditTemplate(templateData)">
                                        @displayText
                                    </span>
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Actions" Width="140" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                    }
                                    <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" OnClick="@(() => EditTemplate(templateData))" Class="ml-2" />
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" OnClick="@(() => DeleteTemplate(templateData))" Class="ml-2" />
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </GenericGrid>
                </div>

                <MudGrid>
                    <MudItem xs="12" Class="mt-6 ml-6">
                        <GenericButton Variant="Variant.Filled" OnClick="@ToggleCardVisibility" ButtonStyle="margin-top: 20px; margin-bottom: 20px; display: block; width: 200px;">
                            @Localizer["+ New Template"]
                        </GenericButton>
                    </MudItem>

                    @if (isAddSectionVisible)
                    {
                        <MudGrid GutterSize="16px" Class="mt-4 ml-2 mr-2">
                            <MudItem xs="12" sm="2" md="2">
                                <MudText Typo="Typo.h6" Class="mb-2">Sections</MudText>
                                <MudList T="TemplateSection" Class="medical-sections">
                                    @foreach (var sectionDisplay in TemplateSections)
                                    {
                                        <MudListItem Class="custom-disabled-listheader-item" Disabled="true"><b>@sectionDisplay.SectionName</b></MudListItem>
                                        @foreach (var field in sectionDisplay.Fields)
                                        {
                                            <MudListItem Class="custom-disabled-list-item" Disabled="true" Value="field" Style="color:black;">@field.FieldName</MudListItem>
                                        }
                                    }
                                </MudList>
                            </MudItem>

                            <MudItem xs="12" sm="6" md="6" Style="max-width: 1000px;">
                                <MudText Typo="Typo.h6" Class="mb-2">Section Settings</MudText>
                                <MudCard Style="width: 100%;">
                                    <MudCardContent>
                                        <MudGrid Style="padding-bottom: 10px">
                                            <MudItem xs="12" sm="6" md="6" Spacing="2">
                                                <MudTextField Label="@Localizer["Template Name"]"
                                                              @bind-Value="@templateName"
                                                              Required="true"
                                                              Variant="Variant.Outlined"
                                                              Class="compact-textfield"
                                                              InputStyle="text-align: center;">
                                                </MudTextField>
                                            </MudItem>

                                            <MudItem xs="12" sm="4" md="4" Class="d-flex align-items-center">
                                                <div style="display: flex; align-items: center; gap: 8px; width: 100%;">
                                                    <SfDropDownList TItem="string" TValue="string"
                                                                    Placeholder="Select Visit Type"
                                                                    Width="300px"
                                                                    DataSource="@VisitTypes.Select(v => v.VisitName).ToList()"
                                                                    Value="@SelectedVisitType"
                                                                    ValueChanged="OnVisitTypeChange"
                                                                    CssClass="visit-type-dropdown">
                                                    </SfDropDownList>
                                                </div>
                                            </MudItem>
                                        </MudGrid>
                                        
                                        <!-- Headers Container -->
                                        <div class="headers-container">
                                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                                <MudText Typo="Typo.h5" Class="template-headers-title">Template Headers</MudText>
                                                <MudButton Size="Size.Small" OnClick="@AddSectionHeader" Color="Color.Primary" Typo="Typo.body2">
                                                    Add Header
                                                </MudButton>
                                            </div>

                                            @for (int j = 0; j < TemplateSections.Count; j++)
                                            {
                                                var sectionIndex = j;
                                                <div class="header-section-card">
                                                    <MudGrid Class="mt-1 align-items-center" Style="padding-top:0px;">
                                                        <MudItem xs="6" Class="d-flex align-items-center">
                                                            <MudButton OnClick="@(() => ToggleSection(sectionIndex))" Color="Color.Primary" Variant="Variant.Text">
                                                                <MudIcon Icon="@(TemplateSections[sectionIndex].IsExpanded ? Icons.Material.Filled.ExpandLess : Icons.Material.Filled.ExpandMore)" Class="mr-2" />
                                                                <MudText Style="font-weight:bold; font-size: 1.2rem; margin-left: 8px;">@TemplateSections[sectionIndex].SectionName</MudText>
                                                            </MudButton>
                                                        </MudItem>
                                                        <MudItem xs="6" Class="d-flex align-items-center justify-content-end">
                                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                           Color="Color.Error"
                                                                           OnClick="@(() => DeleteSectionConfirmation(sectionIndex))"
                                                                           Class="ml-2" />
                                                        </MudItem>
                                                    </MudGrid>

                                                    @if (TemplateSections[sectionIndex].IsExpanded)
                                                    {
                                                        <MudGrid>
                                                            <MudItem xs="12">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <MudText Typo="Typo.body2" Style="margin-left: 60px;">Header Title</MudText>
                                                                    <MudItem xs="6" Class="pa-0">
                                                                        <MudTextField @bind-Value="@TemplateSections[sectionIndex].SectionName"
                                                                                      Variant="Variant.Outlined"
                                                                                      Margin="Margin.Dense" Style="margin-right: 60px;" />
                                                                    </MudItem>
                                                                </div>
                                                            </MudItem>
                                                        </MudGrid>

                                                        <MudGrid Class="mt-2">
                                                            <MudItem xs="12" Class="d-flex align-items-center justify-content-end">
                                                                <MudButton Size="Size.Small" Color="Color.Primary" OnClick="@(() => AddTextBox(sectionIndex))" Typo="Typo.body2">
                                                                    Add Section
                                                                </MudButton>
                                                            </MudItem>
                                                        </MudGrid>

                                                        @for (int i = 0; i < TemplateSections[sectionIndex].Fields.Count; i++)
                                                        {
                                                            var fieldIndex = i;
                                                            <MudCard Class="mt-3" Style="background-color: rgb(249, 250, 252);">
                                                                <MudCardHeader Class="py-1">
                                                                    <MudItem xs="12">
                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <MudText Typo="Typo.body1">@TemplateSections[sectionIndex].Fields[fieldIndex].FieldName</MudText>
                                                                            <MudItem Class="ml-auto pa-0">
                                                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                                               Color="Color.Error"
                                                                                               OnClick="@(() => RemoveTextBox(fieldIndex, sectionIndex))"
                                                                                               Class="ml-2" />
                                                                            </MudItem>
                                                                        </div>
                                                                    </MudItem>
                                                                </MudCardHeader>
                                                                <MudDivider DividerType="DividerType.Middle" Style="background-color: gray;" />
                                                                <MudCardContent>
                                                                    <MudGrid>
                                                                        <MudItem xs="12">
                                                                            <div class="d-flex justify-content-between align-items-center">
                                                                                <MudText Typo="Typo.body2" Style="margin-right: auto;">Section Title</MudText>
                                                                                <MudItem xs="6" Class="pa-0">
                                                                                    <MudTextField @bind-Value="@TemplateSections[sectionIndex].Fields[fieldIndex].FieldName"
                                                                                                  Variant="Variant.Outlined"
                                                                                                  Margin="Margin.Dense" />
                                                                                </MudItem>
                                                                            </div>
                                                                        </MudItem>
                                                                    </MudGrid>

                                                                    <div class="d-flex align-items-center mt-2">
                                                                        <MudText Class="mb-1" Typo="Typo.body2">Section style</MudText>
                                                                        <div class="ml-auto">
                                                                            <MudButtonGroup Color="Color.Primary" OverrideStyles="_overrideStyles">
                                                                                @foreach (var style in new[] { "Auto", "Bullet", "Paragraph" })
                                                                                {
                                                                                    var currentStyle = style;
                                                                                    <MudButton Size="Size.Small"
                                                                                               Color="Color.Primary"
                                                                                               Variant="@GetButtonVariant(currentStyle, fieldIndex, sectionIndex)"
                                                                                               OnClick="@(() => SetSectionStyle(currentStyle, fieldIndex, sectionIndex))"
                                                                                               Typo="Typo.body2">@Localizer[currentStyle]</MudButton>
                                                                                }
                                                                            </MudButtonGroup>
                                                                        </div>
                                                                    </div>

                                                                    <MudItem xs="12" Class="mt-1">
                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <MudText Typo="Typo.body2" Style="margin-right: auto;">Custom Instructions</MudText>
                                                                            <MudItem xs="8" Class="pa-0">
                                                                                <MudTextField @bind-Value="TemplateSections[sectionIndex].Fields[fieldIndex].Instructions"
                                                                                              Variant="Variant.Outlined"
                                                                                              Margin="Margin.Dense"
                                                                                              Class="mt-2" />
                                                                            </MudItem>
                                                                        </div>
                                                                    </MudItem>
                                                                </MudCardContent>
                                                            </MudCard>
                                                        }
                                                    }
                                                </div>
                                            }
                                        </div>

                                        <div style="display: flex; justify-content: flex-end; align-items: flex-end; margin-top: 32px;">
                                            <MudButton Size="Size.Large"
                                                       Color="Color.Primary"
                                                       OnClick="@SubmitForm"
                                                       Typo="Typo.body1"
                                                       Style="padding: 10px 24px; font-size: 1rem;">
                                                <MudIcon Icon="@Icons.Material.Filled.Send" Class="mr-2" />
                                                @Localizer["ADD"]
                                            </MudButton>
                                        </div>
                                    </MudCardContent>
                                </MudCard>
                            </MudItem>
                        </MudGrid>
                    }
                </MudGrid>
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>

@if (showConfirmDialog)
{
    <div style="position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background-color: rgba(0, 0, 0, 0.4); z-index: 1500; display: flex; justify-content: center; align-items: center;">
        <MudPaper Elevation="24" Class="pa-6" Style="background: white; border-radius: 12px; min-width: 300px; max-width: 90vw;">
            <MudText Typo="Typo.h6" Class="mb-2">@confirmDialogTitle</MudText>
            <MudText Typo="Typo.body1" Class="mb-4">@confirmDialogMessage</MudText>
            <div class="d-flex justify-content-end mt-4">
                <MudButton OnClick="CancelConfirmation" Color="Color.Default" Variant="Variant.Outlined" Class="mr-2">Cancel</MudButton>
                <MudButton OnClick="ConfirmAction" Color="Color.Primary" Variant="Variant.Filled">Confirm</MudButton>
            </div>
        </MudPaper>
    </div>
}
<style>
    /* Original styles */
    .medical-sections .e-list-item {
        padding: 12px 0;
        color: #5f6368;
        font-size: 14px;
        border-bottom: 1px solid #ebedf0;
    }

    .custom-disabled-list-item.mud-list-item-disabled {
        color: silver !important;
        opacity: 1 !important;
        cursor: default;
        pointer-events: none;
    }

    .custom-disabled-listheader-item.mud-list-item-disabled {
        color: grey !important;
        opacity: 1 !important;
        cursor: default;
        pointer-events: none;
    }

    .headers-container {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        background-color: #fafafa;
        margin-bottom: 16px;
        margin-top: 8px !important; /* Reduced space above headers container */
    }

    /* FIXED: Make Template Name and Visit Type dropdowns uniform size */
    .compact-textfield {
        width: 300px !important; /* Same width as visit type dropdown */
    }

        .compact-textfield .mud-input-control {
            height: 36px !important;
            min-height: 36px !important;
            width: 100% !important;
        }

        .compact-textfield .mud-input-control-input-container {
            height: 36px !important;
            min-height: 36px !important;
        }

        .compact-textfield .mud-input {
            height: 100% !important;
            padding: 0 12px !important; /* Consistent padding with dropdown */
            font-size: 14px !important; /* Consistent font size */
            display: flex !important;
            align-items: center !important;
            line-height: normal !important;
        }

        .compact-textfield .mud-input-label {
            font-size: 12px !important;
            top: -8px !important;
        }

    /* FIXED: Visit Type dropdown - consistent with template name */
    .visit-type-dropdown {
        border: 2px solid #1976d2 !important;
        border-radius: 4px !important; /* Consistent border radius */
        background-color: white !important;
        height: 36px !important;
        display: flex !important;
        align-items: center !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        width: 300px !important; /* Explicit width matching template name */
    }

        .visit-type-dropdown .e-input-group {
            border: none !important;
            background: transparent !important;
            height: 36px !important;
        }

            .visit-type-dropdown .e-input-group.e-control-wrapper {
                height: 36px !important;
            }

        .visit-type-dropdown .e-input {
            height: 36px !important;
            padding: 0 12px !important;
            border: none !important;
            background: transparent !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            color: #333 !important;
        }

        .visit-type-dropdown .e-ddl-icon {
            color: #1976d2 !important;
            font-size: 16px !important;
        }

    /* FIXED: Template Headers title - reduced space above */
    .template-headers-title {
        font-size: 1.3rem !important;
        font-weight: 600 !important;
        margin-left: 0px !important;
        color: #1976d2 !important;
        margin-bottom: 12px !important;
        margin-top: 4px !important; /* Reduced top margin */
    }

    /* FIXED: Uniform alignment for all form labels */
    .form-label-left {
        text-align: left !important;
        display: flex !important;
        align-items: center !important;
        padding-left: 0 !important;
        margin-left: 0 !important;
        font-weight: 500 !important;
        color: #424242 !important;
    }

    /* FIXED: Header section cards - better segregation and consistent spacing */
    .header-section-card {
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 16px;
        padding: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        position: relative;
    }

        .header-section-card:nth-child(odd) {
            background-color: #fafafa;
            border-color: #e8f5e8;
        }

        .header-section-card:nth-child(even) {
            background-color: #fff;
            border-color: #e3f2fd;
        }

        .header-section-card:last-child {
            margin-bottom: 0;
        }

    /* FIXED: Consistent spacing and alignment for header title row */
    .header-title-row {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        margin-bottom: 16px !important;
    }

        .header-title-row .form-label-left {
            flex: 0 0 120px !important; /* Fixed width for label */
            margin-right: 16px !important;
        }

        .header-title-row .mud-item {
            flex: 1 !important;
            padding: 0 !important;
        }

    /* FIXED: Section content alignment - all fields aligned consistently */
    .section-content-row {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        margin-bottom: 12px !important;
    }

        .section-content-row .form-label-left {
            flex: 0 0 120px !important; /* Same width as header title label */
            margin-right: 16px !important;
        }

        .section-content-row .mud-item {
            flex: 1 !important;
            padding: 0 !important;
        }

    /* FIXED: Section style buttons - reduced size and better alignment */
    .section-style-buttons {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-end !important;
    }

        .section-style-buttons .mud-button-group {
            margin-left: auto !important;
        }

        .section-style-buttons .mud-button {
            font-size: 0.75rem !important; /* Smaller font size */
            padding: 4px 8px !important; /* Reduced padding */
            min-width: 60px !important; /* Consistent button width */
            height: 32px !important; /* Reduced height */
        }

    /* FIXED: Instructions field alignment */
    .instructions-row {
        display: flex !important;
        align-items: flex-start !important;
        justify-content: space-between !important;
        margin-bottom: 12px !important;
    }

        .instructions-row .form-label-left {
            flex: 0 0 120px !important;
            margin-right: 16px !important;
            padding-top: 8px !important; /* Align with textarea */
        }

        .instructions-row .mud-item {
            flex: 1 !important;
            padding: 0 !important;
        }

    /* FIXED: Consistent card styling for sections */
    .section-card {
        background-color: rgb(249, 250, 252) !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 6px !important;
        margin-bottom: 12px !important;
    }

        .section-card .mud-card-header {
            background-color: #f5f5f5 !important;
            border-bottom: 1px solid #e0e0e0 !important;
            padding: 8px 16px !important;
        }

        .section-card .mud-card-content {
            padding: 16px !important;
        }

    /* FIXED: Better spacing between sections */
    .mud-grid .mud-item[class*="sm-2"] {
        padding-right: 12px !important;
    }

    .mud-grid .mud-item[class*="sm-6"] {
        padding-left: 8px !important;
    }

    /* FIXED: Consistent form field styling */
    .mud-input-control {
        margin-bottom: 0 !important;
        height: 36px !important;
    }

        .mud-input-control .mud-input {
            height: 36px !important;
            padding: 0 12px !important;
            font-size: 14px !important;
        }

    /* FIXED: Better button alignment and spacing */
    .add-section-button {
        display: flex !important;
        justify-content: flex-end !important;
        align-items: center !important;
        margin-top: 8px !important;
        margin-bottom: 16px !important;
    }

        .add-section-button .mud-button {
            font-size: 0.875rem !important;
            padding: 6px 12px !important;
            height: 32px !important;
        }

    /* FIXED: Uniform header expansion button styling */
    .header-expand-button {
        display: flex !important;
        align-items: center !important;
        padding: 8px 12px !important;
        border-radius: 4px !important;
    }

        .header-expand-button .mud-text {
            font-size: 1.1rem !important;
            font-weight: 600 !important;
            color: #1976d2 !important;
            margin-left: 8px !important;
        }

    /* FIXED: Better focus states */
    .mud-input:focus,
    .e-input:focus {
        border-color: #1976d2 !important;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
    }

    /* FIXED: Enhanced accessibility */
    .mud-button:focus-visible,
    .mud-icon-button:focus-visible {
        outline: 2px solid #1976d2;
        outline-offset: 2px;
    }

    /* FIXED: Responsive design improvements */
    @@media (max-width: 600px) {
        .compact-textfield, .visit-type-dropdown

    {
        width: 100% !important;
    }

    .form-label-left {
        flex: 0 0 100px !important;
        font-size: 0.875rem !important;
    }

    .header-title-row .form-label-left,
    .section-content-row .form-label-left,
    .instructions-row .form-label-left {
        flex: 0 0 100px !important;
    }

    .section-style-buttons .mud-button {
        font-size: 0.7rem !important;
        padding: 3px 6px !important;
        min-width: 50px !important;
    }

    }

    @@media (max-width: 960px) {
        .mud-grid .mud-item[class*="sm-2"]

    {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 8px !important;
        padding-right: 0 !important;
    }

    .mud-grid .mud-item[class*="sm-6"] {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        padding-left: 0 !important;
    }

    }

    /* FIXED: Smooth transitions for better UX */
    .header-section-card,
    .section-card,
    .mud-button {
        transition: all 0.2s ease-in-out;
    }

        .header-section-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* FIXED: Enhanced button styling */
        .mud-button.mud-button-filled.mud-button-color-primary {
            background: #1976d2 !important;
            box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3) !important;
        }

            .mud-button.mud-button-filled.mud-button-color-primary:hover {
                background: #1565c0 !important;
                box-shadow: 0 3px 6px rgba(25, 118, 210, 0.4) !important;
            }

        /* FIXED: Consistent grid alignment */
        .header-section-card .mud-grid:first-child {
            margin-top: 0 !important;
            align-items: center !important;
            min-height: 40px !important;
        }

    /* FIXED: Better visual hierarchy */
    .mud-text.mud-typography-h6 {
        color: #424242 !important;
        font-weight: 500 !important;
        margin-bottom: 8px !important;
    }

    /* FIXED: Consistent delete button styling */
    .mud-icon-button {
        height: 36px !important;
        width: 36px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
</style>